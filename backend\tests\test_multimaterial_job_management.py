"""
Integration Tests for Multi-Material Job Management
==================================================

This module contains comprehensive tests for the multi-material job management system
including job creation with 3 CLI files, CLI file parsing per drum, layer count validation,
job status tracking, cancel/error handling, and drum coordination tests.

Based on Stage 2 requirements from the implementation documentation.
"""

import pytest
import asyncio
import uuid
from unittest.mock import Mock, AsyncMock, patch
from fastapi.testclient import TestClient

from app.main import app
from app.models.multilayer_job import (
    MultiMaterialJobState, 
    JobStatus, 
    LayerData, 
    DrumState,
    multimaterial_job_state
)
from app.services.multilayer_job_manager import MultiMaterialJobManager, MultiMaterialJobError
from services.cli_parser import CliParserService, ParsedCliFile, CliLayer, Point, Polyline, Hatch
from services.recoater_client import RecoaterConnectionError, RecoaterAPIError


@pytest.fixture
def client():
    """Create a test client for the FastAPI app."""
    return TestClient(app)


@pytest.fixture
def mock_recoater_client():
    """Create a mock recoater client."""
    mock_client = Mock()
    mock_client.upload_drum_geometry = Mock(return_value={"success": True})
    return mock_client


@pytest.fixture
def sample_cli_layers():
    """Create sample CLI layers for testing."""
    return [
        CliLayer(
            z_height=0.1,
            polylines=[
                Polyline(
                    part_id=1,
                    direction=0,
                    points=[Point(x=0.0, y=0.0), Point(x=10.0, y=10.0)]
                )
            ],
            hatches=[]
        ),
        CliLayer(
            z_height=0.2,
            polylines=[],
            hatches=[
                Hatch(
                    group_id=1,
                    lines=[(Point(x=1.0, y=1.0), Point(x=9.0, y=9.0))]
                )
            ]
        ),
        CliLayer(
            z_height=0.3,
            polylines=[
                Polyline(
                    part_id=2,
                    direction=1,
                    points=[Point(x=5.0, y=5.0), Point(x=15.0, y=15.0)]
                )
            ],
            hatches=[]
        )
    ]


@pytest.fixture
def sample_parsed_cli_files(sample_cli_layers):
    """Create sample parsed CLI files with different layer counts."""
    return {
        "file_1": ParsedCliFile(
            header_lines=["$$HEADERSTART", "$$BINARY 0", "$$UNITS 1.0", "$$HEADEREND"],
            is_aligned=False,
            layers=sample_cli_layers[:2]  # 2 layers
        ),
        "file_2": ParsedCliFile(
            header_lines=["$$HEADERSTART", "$$BINARY 0", "$$UNITS 1.0", "$$HEADEREND"],
            is_aligned=False,
            layers=sample_cli_layers  # 3 layers
        ),
        "file_3": ParsedCliFile(
            header_lines=["$$HEADERSTART", "$$BINARY 0", "$$UNITS 1.0", "$$HEADEREND"],
            is_aligned=False,
            layers=sample_cli_layers[:1]  # 1 layer
        )
    }


@pytest.fixture
def cli_file_cache(sample_parsed_cli_files):
    """Create a CLI file cache for testing."""
    return sample_parsed_cli_files


class TestMultiMaterialJobState:
    """Test multi-material job state management."""
    
    def test_job_state_initialization(self):
        """Test job state initialization."""
        job_state = MultiMaterialJobState()
        
        assert job_state.is_active is False
        assert job_state.status == JobStatus.IDLE
        assert job_state.current_layer == 0
        assert job_state.total_layers == 0
        assert len(job_state.drums) == 3
        assert all(drum_id in job_state.drums for drum_id in [0, 1, 2])
    
    def test_progress_calculation(self):
        """Test progress percentage calculation."""
        job_state = MultiMaterialJobState()
        job_state.total_layers = 10
        job_state.current_layer = 5
        
        progress = job_state.get_progress_percentage()
        assert progress == 40.0  # (5-1)/10 * 100
    
    def test_all_drums_ready(self):
        """Test all drums ready check."""
        job_state = MultiMaterialJobState()
        
        # Initially not ready
        assert not job_state.all_drums_ready()
        
        # Set all drums ready
        for drum in job_state.drums.values():
            drum.is_ready = True
        
        assert job_state.all_drums_ready()
    
    def test_depleted_drums_detection(self):
        """Test depleted drums detection."""
        job_state = MultiMaterialJobState()
        job_state.current_layer = 3
        job_state.remaining_layers = {
            0: [Mock(), Mock()],  # 2 layers, depleted at layer 3
            1: [Mock(), Mock(), Mock()],  # 3 layers, not depleted
            2: []  # No layers, depleted
        }
        
        depleted = job_state.get_depleted_drums()
        assert 0 in depleted
        assert 2 in depleted
        assert 1 not in depleted
    
    def test_job_reset(self):
        """Test job state reset."""
        job_state = MultiMaterialJobState()
        job_state.is_active = True
        job_state.current_layer = 5
        job_state.status = JobStatus.RECOATER_ACTIVE
        
        job_state.reset_job()
        
        assert job_state.is_active is False
        assert job_state.current_layer == 0
        assert job_state.status == JobStatus.IDLE
        assert job_state.error_message == ""


class TestMultiMaterialJobManager:
    """Test multi-material job manager functionality."""
    
    @pytest.mark.asyncio
    async def test_job_initialization_success(self, mock_recoater_client, cli_file_cache):
        """Test successful job initialization with 3 CLI files."""
        job_manager = MultiMaterialJobManager(mock_recoater_client)
        
        file_ids = {0: "file_1", 1: "file_2", 2: "file_3"}
        
        job_id = await job_manager.initialize_job(file_ids, cli_file_cache)
        
        assert job_id is not None
        assert job_manager.job_state.status == JobStatus.WAITING_FOR_PRINT_START
        assert job_manager.job_state.total_layers == 3  # Max of 2, 3, 1
        assert len(job_manager.job_state.file_ids) == 3
        assert job_manager.job_state.file_ids == file_ids
    
    @pytest.mark.asyncio
    async def test_job_initialization_invalid_drum_count(self, mock_recoater_client, cli_file_cache):
        """Test job initialization with invalid drum count."""
        job_manager = MultiMaterialJobManager(mock_recoater_client)
        
        # Only 2 files instead of 3
        file_ids = {0: "file_1", 1: "file_2"}
        
        with pytest.raises(MultiMaterialJobError, match="exactly 3 CLI files"):
            await job_manager.initialize_job(file_ids, cli_file_cache)
    
    @pytest.mark.asyncio
    async def test_job_initialization_invalid_drum_ids(self, mock_recoater_client, cli_file_cache):
        """Test job initialization with invalid drum IDs."""
        job_manager = MultiMaterialJobManager(mock_recoater_client)
        
        # Invalid drum IDs
        file_ids = {0: "file_1", 1: "file_2", 3: "file_3"}  # 3 is invalid
        
        with pytest.raises(MultiMaterialJobError, match="Drum IDs must be 0, 1, and 2"):
            await job_manager.initialize_job(file_ids, cli_file_cache)
    
    @pytest.mark.asyncio
    async def test_job_initialization_missing_file(self, mock_recoater_client, cli_file_cache):
        """Test job initialization with missing CLI file."""
        job_manager = MultiMaterialJobManager(mock_recoater_client)
        
        file_ids = {0: "file_1", 1: "file_2", 2: "missing_file"}
        
        with pytest.raises(MultiMaterialJobError, match="not found in cache"):
            await job_manager.initialize_job(file_ids, cli_file_cache)
    
    @pytest.mark.asyncio
    async def test_job_start_success(self, mock_recoater_client, cli_file_cache):
        """Test successful job start."""
        job_manager = MultiMaterialJobManager(mock_recoater_client)
        
        # Initialize job first
        file_ids = {0: "file_1", 1: "file_2", 2: "file_3"}
        await job_manager.initialize_job(file_ids, cli_file_cache)
        
        # Start job
        success = await job_manager.start_job()
        
        assert success is True
        assert job_manager.job_state.is_active is True
        assert job_manager.job_state.status == JobStatus.RECOATER_ACTIVE
        assert job_manager.job_state.start_time is not None
    
    @pytest.mark.asyncio
    async def test_job_start_invalid_status(self, mock_recoater_client):
        """Test job start with invalid status."""
        job_manager = MultiMaterialJobManager(mock_recoater_client)
        
        # Try to start without initialization
        with pytest.raises(MultiMaterialJobError, match="Cannot start job in status"):
            await job_manager.start_job()
    
    @pytest.mark.asyncio
    async def test_layer_upload_success(self, mock_recoater_client, cli_file_cache):
        """Test successful layer upload to all drums."""
        job_manager = MultiMaterialJobManager(mock_recoater_client)
        
        # Initialize and start job
        file_ids = {0: "file_1", 1: "file_2", 2: "file_3"}
        await job_manager.initialize_job(file_ids, cli_file_cache)
        await job_manager.start_job()
        
        # Upload current layer
        success = await job_manager.upload_current_layer_to_all_drums()
        
        assert success is True
        assert mock_recoater_client.upload_drum_geometry.call_count == 3  # Called for each drum
        
        # Check drum states
        for drum in job_manager.job_state.drums.values():
            assert drum.is_uploaded is True
            assert drum.is_ready is True
    
    @pytest.mark.asyncio
    async def test_layer_upload_with_depleted_drums(self, mock_recoater_client, cli_file_cache):
        """Test layer upload with depleted drums."""
        job_manager = MultiMaterialJobManager(mock_recoater_client)
        
        # Initialize and start job
        file_ids = {0: "file_1", 1: "file_2", 2: "file_3"}
        await job_manager.initialize_job(file_ids, cli_file_cache)
        await job_manager.start_job()
        
        # Move to layer 3 (drum 0 and 2 will be depleted)
        job_manager.job_state.current_layer = 3
        
        success = await job_manager.upload_current_layer_to_all_drums()
        
        assert success is True
        assert mock_recoater_client.upload_drum_geometry.call_count == 3
        
        # Check depleted drum status
        assert job_manager.job_state.drums[0].status == "depleted"
        assert job_manager.job_state.drums[1].status == "ready"
        assert job_manager.job_state.drums[2].status == "depleted"
    
    @pytest.mark.asyncio
    async def test_layer_upload_failure(self, mock_recoater_client, cli_file_cache):
        """Test layer upload failure handling."""
        job_manager = MultiMaterialJobManager(mock_recoater_client)
        
        # Mock upload failure for drum 1
        def upload_side_effect(drum_id, **kwargs):
            if drum_id == 1:
                raise RecoaterConnectionError("Connection failed")
            return {"success": True}
        
        mock_recoater_client.upload_drum_geometry.side_effect = upload_side_effect
        
        # Initialize and start job
        file_ids = {0: "file_1", 1: "file_2", 2: "file_3"}
        await job_manager.initialize_job(file_ids, cli_file_cache)
        await job_manager.start_job()
        
        # Upload current layer
        success = await job_manager.upload_current_layer_to_all_drums()
        
        assert success is False
        assert job_manager.job_state.status == JobStatus.ERROR
        assert job_manager.job_state.drums[1].status == "error"
    
    @pytest.mark.asyncio
    async def test_job_cancellation(self, mock_recoater_client, cli_file_cache):
        """Test job cancellation."""
        job_manager = MultiMaterialJobManager(mock_recoater_client)
        
        # Initialize and start job
        file_ids = {0: "file_1", 1: "file_2", 2: "file_3"}
        await job_manager.initialize_job(file_ids, cli_file_cache)
        await job_manager.start_job()
        
        # Cancel job
        success = await job_manager.cancel_job()
        
        assert success is True
        assert job_manager.job_state.is_active is False
        assert job_manager.job_state.status == JobStatus.CANCELLED
        
        # Check all drums are cancelled
        for drum in job_manager.job_state.drums.values():
            assert drum.status == "cancelled"
    
    def test_job_status_retrieval(self, mock_recoater_client, cli_file_cache):
        """Test job status retrieval."""
        job_manager = MultiMaterialJobManager(mock_recoater_client)
        
        status = job_manager.get_job_status()
        
        assert "job_id" in status
        assert "is_active" in status
        assert "status" in status
        assert "drums" in status
        assert len(status["drums"]) == 3
        assert "waiting_states" in status
    
    def test_error_clearing(self, mock_recoater_client):
        """Test error state clearing."""
        job_manager = MultiMaterialJobManager(mock_recoater_client)
        
        # Set error state
        job_manager.job_state.set_error("Test error")
        job_manager.job_state.drums[0].status = "error"
        job_manager.job_state.drums[0].error_message = "Drum error"
        
        # Clear error
        success = job_manager.clear_error()
        
        assert success is True
        assert job_manager.job_state.error_message == ""
        assert job_manager.job_state.drums[0].status == "idle"
        assert job_manager.job_state.drums[0].error_message == ""


class TestMultiMaterialAPIEndpoints:
    """Test multi-material API endpoints."""

    @patch('app.api.print.get_job_manager')
    @patch('app.api.print.cli_file_cache')
    def test_start_multimaterial_job_success(self, mock_cache, mock_get_manager, client):
        """Test successful multi-material job start via API."""
        # Setup mocks
        mock_job_manager = AsyncMock()
        mock_job_manager.initialize_job = AsyncMock(return_value="test-job-id")
        mock_job_manager.start_job = AsyncMock(return_value=True)
        mock_get_manager.return_value = mock_job_manager

        mock_cache.__contains__ = Mock(return_value=True)

        # Make request
        response = client.post(
            "/api/v1/print/cli/start-multimaterial-job",
            json={"file_ids": {0: "file1", 1: "file2", 2: "file3"}}
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["job_id"] == "test-job-id"
        assert "started successfully" in data["message"]

    @patch('app.api.print.get_job_manager')
    def test_start_multimaterial_job_invalid_drums(self, mock_get_manager, client):
        """Test multi-material job start with invalid drum configuration."""
        response = client.post(
            "/api/v1/print/cli/start-multimaterial-job",
            json={"file_ids": {0: "file1", 1: "file2"}}  # Missing drum 2
        )

        assert response.status_code == 400
        assert "exactly 3 files" in response.json()["detail"]

    @patch('app.api.print.get_job_manager')
    def test_get_multimaterial_job_status(self, mock_get_manager, client):
        """Test getting multi-material job status via API."""
        # Setup mock
        mock_job_manager = Mock()
        mock_job_manager.get_job_status.return_value = {
            "job_id": "test-job-id",
            "is_active": True,
            "status": "recoater_active",
            "current_layer": 2,
            "total_layers": 10,
            "progress_percentage": 10.0,
            "estimated_time_remaining": 300.0,
            "start_time": 1234567890.0,
            "error_message": "",
            "drums": {
                0: {"file_id": "file1", "is_ready": True, "is_uploaded": True, "status": "ready",
                    "error_message": "", "last_layer_uploaded": 2, "total_layers": 8},
                1: {"file_id": "file2", "is_ready": True, "is_uploaded": True, "status": "ready",
                    "error_message": "", "last_layer_uploaded": 2, "total_layers": 10},
                2: {"file_id": "file3", "is_ready": True, "is_uploaded": True, "status": "ready",
                    "error_message": "", "last_layer_uploaded": 2, "total_layers": 5}
            },
            "waiting_states": {
                "waiting_for_print_start": False,
                "waiting_for_layer_complete": False,
                "waiting_for_galvo": False
            }
        }
        mock_get_manager.return_value = mock_job_manager

        # Make request
        response = client.get("/api/v1/print/multimaterial-job/status")

        assert response.status_code == 200
        data = response.json()
        assert data["job_id"] == "test-job-id"
        assert data["is_active"] is True
        assert data["current_layer"] == 2
        assert data["total_layers"] == 10
        assert len(data["drums"]) == 3

    @patch('app.api.print.get_job_manager')
    def test_cancel_multimaterial_job(self, mock_get_manager, client):
        """Test cancelling multi-material job via API."""
        # Setup mock
        mock_job_manager = AsyncMock()
        mock_job_manager.cancel_job = AsyncMock(return_value=True)
        mock_get_manager.return_value = mock_job_manager

        # Make request
        response = client.post("/api/v1/print/multimaterial-job/cancel")

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "cancelled successfully" in data["message"]

    @patch('app.api.print.get_job_manager')
    def test_clear_multimaterial_job_error(self, mock_get_manager, client):
        """Test clearing multi-material job error via API."""
        # Setup mock
        mock_job_manager = Mock()
        mock_job_manager.clear_error.return_value = True
        mock_get_manager.return_value = mock_job_manager

        # Make request
        response = client.post("/api/v1/print/multimaterial-job/clear-error")

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "cleared successfully" in data["message"]

    @patch('app.api.print.get_job_manager')
    def test_get_drum_status(self, mock_get_manager, client):
        """Test getting individual drum status via API."""
        # Setup mock
        mock_job_manager = Mock()
        mock_job_manager.get_job_status.return_value = {
            "drums": {
                0: {"file_id": "file1", "is_ready": True, "is_uploaded": True, "status": "ready",
                    "error_message": "", "last_layer_uploaded": 2, "total_layers": 8},
                1: {"file_id": "file2", "is_ready": False, "is_uploaded": False, "status": "error",
                    "error_message": "Upload failed", "last_layer_uploaded": 1, "total_layers": 10},
                2: {"file_id": "file3", "is_ready": True, "is_uploaded": True, "status": "depleted",
                    "error_message": "", "last_layer_uploaded": 2, "total_layers": 5}
            }
        }
        mock_get_manager.return_value = mock_job_manager

        # Test drum 0
        response = client.get("/api/v1/print/multimaterial-job/drum-status/0")
        assert response.status_code == 200
        data = response.json()
        assert data["drum_id"] == 0
        assert data["file_id"] == "file1"
        assert data["status"] == "ready"

        # Test drum 1 (error state)
        response = client.get("/api/v1/print/multimaterial-job/drum-status/1")
        assert response.status_code == 200
        data = response.json()
        assert data["drum_id"] == 1
        assert data["status"] == "error"
        assert data["error_message"] == "Upload failed"

        # Test invalid drum ID
        response = client.get("/api/v1/print/multimaterial-job/drum-status/5")
        assert response.status_code == 422  # Validation error for drum_id > 2


class TestLayerDataGeneration:
    """Test layer data generation and CLI creation."""

    def test_layer_data_cli_generation(self, sample_cli_layers):
        """Test CLI data generation from LayerData."""
        layer_data = LayerData(
            layer_number=1,
            z_height=0.1,
            cli_layer=sample_cli_layers[0],
            drum_id=0
        )

        header_lines = ["$$HEADERSTART", "$$BINARY 0", "$$UNITS 1.0", "$$HEADEREND"]
        cli_data = layer_data.generate_cli_data(header_lines)

        assert isinstance(cli_data, bytes)
        assert b"$$HEADEREND" in cli_data
        assert b"$$LAYER" in cli_data
        assert b"0.1" in cli_data  # Z height

    def test_empty_cli_generation(self, mock_recoater_client):
        """Test empty CLI generation for depleted drums."""
        job_manager = MultiMaterialJobManager(mock_recoater_client)

        empty_cli = job_manager._generate_empty_cli()

        assert isinstance(empty_cli, bytes)
        assert b"$$HEADEREND" in empty_cli
        assert b"$$LAYER 0.0" in empty_cli
        assert b"Empty Layer" in empty_cli


class TestDrumCoordination:
    """Test drum coordination and synchronization."""

    def test_drum_state_management(self):
        """Test drum state management."""
        drum_state = DrumState(drum_id=0)

        assert drum_state.drum_id == 0
        assert drum_state.is_ready is False
        assert drum_state.status == "idle"

        # Test reset
        drum_state.is_ready = True
        drum_state.status = "uploaded"
        drum_state.error_message = "Test error"

        drum_state.reset()

        assert drum_state.is_ready is False
        assert drum_state.status == "idle"
        assert drum_state.error_message == ""

    @pytest.mark.asyncio
    async def test_upload_delay_timing(self, mock_recoater_client, cli_file_cache):
        """Test that upload delay is properly implemented."""
        import time

        job_manager = MultiMaterialJobManager(mock_recoater_client)
        job_manager.drum_upload_delay = 0.1  # Reduce delay for testing

        # Initialize and start job
        file_ids = {0: "file_1", 1: "file_2", 2: "file_3"}
        await job_manager.initialize_job(file_ids, cli_file_cache)
        await job_manager.start_job()

        # Measure upload time
        start_time = time.time()
        await job_manager.upload_current_layer_to_all_drums()
        end_time = time.time()

        # Should take at least 0.2 seconds (2 delays of 0.1s each)
        assert end_time - start_time >= 0.2
        assert mock_recoater_client.upload_drum_geometry.call_count == 3
