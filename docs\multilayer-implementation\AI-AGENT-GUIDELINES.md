# AI Agent Implementation Guidelines

## Overview

This document provides specific guidance for AI agents implementing the multi-material print job system. It complements the existing documentation with AI-specific considerations and implementation patterns.

## AI Agent Development Principles

### 1. **Incremental Implementation Strategy**
- **Always implement in the documented stages** (Stage 1 → Stage 2 → Stage 3 → Stage 4)
- **Complete all tests for each stage** before proceeding to the next
- **Never skip dependency installation** - use package managers, not manual file editing
- **Preserve existing functionality** - add new capabilities without breaking current features

### 2. **Code Organization Patterns**
- **Follow the exact file structure** specified in `04-code-changes.md`
- **Use dependency injection** patterns established in `backend/app/dependencies.py`
- **Maintain separation of concerns** between API, service, and model layers
- **Create dedicated modules** for new functionality rather than modifying existing files extensively

### 3. **Testing Requirements**
- **Write tests first** for new components before implementation
- **Use existing test patterns** found in `frontend/src/views/__tests__/` and `backend/tests/`
- **Mock external dependencies** (OPC UA, Aerosint server) during development
- **Validate each stage** with the test scenarios in `05-testing-guide.md`

## Implementation Checklist

### Stage 1: OPC UA Infrastructure ✅ COMPLETE
- [x] Install `asyncua>=1.0.0` via pip and add to requirements.txt
- [x] Create `backend/app/config/opcua_config.py` for simplified 7-variable configuration
- [x] Implement `backend/app/services/opcua_server.py` with server hosting and management
- [x] Implement `backend/app/services/opcua_coordinator.py` for high-level coordination API
- [x] Add OPC UA server to dependency injection in `backend/app/dependencies.py`
- [x] Write comprehensive unit tests (28 tests) for OPC UA infrastructure
- [x] Test with mock OPC UA client and validate all success criteria

### Stage 2: Multi-Material Job Management
- [ ] Create `backend/app/models/multilayer_job.py` with job state models
- [ ] Implement `backend/app/services/multilayer_job_manager.py` for job lifecycle
- [ ] Add multi-material endpoints to `backend/app/api/print.py`
- [ ] Implement variable layer count logic (total_layers = max of 3 CLI files)
- [ ] Add depleted drum handling (empty CLI upload or skip upload)
- [ ] Write integration tests for job management API endpoints

### Stage 3: Coordination Logic
- [ ] Implement `backend/app/services/coordination_engine.py` for simplified coordination
- [ ] Add Aerosint status polling logic for layer progression
- [ ] Implement backend_error and plc_error flag handling
- [ ] Add comprehensive logging for debugging coordination issues
- [ ] Test coordination logic with mock Aerosint server

### Stage 4: Frontend Integration
- [ ] Create Vue.js components: `MultiLayerJobControl.vue`, `JobProgressDisplay.vue`, `ErrorDisplayPanel.vue`
- [ ] **Implement Critical Error Modal** - Persistent modal for backend_error and plc_error with 'X' button
- [ ] **Add Clear Error Flags Button** - Allow operator to reset error flags after issue resolution
- [ ] Enhance existing `printJobStore.js` with multi-material state management
- [ ] Integrate components into existing `PrintView.vue` without breaking current UI
- [ ] Add 3-file upload capability with validation
- [ ] Write frontend tests using Vitest and Vue Test Utils

## Critical Implementation Notes

### Hardware Constraints
- **3-Drum System**: The recoater has exactly 3 drums (0, 1, 2) - validate this in all UI components
- **ASCII CLI Format**: Hardware requires ASCII CLI format, not binary
- **Variable Layer Counts**: CLI files can have different layer counts - use maximum as total_layers
- **Depleted Drum Handling**: When drums run out of layers, upload empty CLI to prevent memory retention of previous layer within Aerosint
- **Upload Delay**: Add 2-second delay between drum uploads to prevent overloading Aerosint server
- **Synchronization Required**: All 3 drums must be ready before each layer execution

### Error Handling Patterns
- **Use existing error patterns** from `services/recoater_client.py`
- **Simplified Error Architecture** - Use only backend_error and plc_error flags (7-variable approach)
- **Add comprehensive error recovery** for partial failures (some drums succeed, others fail)
- **Maintain error state consistency** across all components
- **Critical: Backend/PLC Error Modal** - Any backend or PLC error must pause operation and show persistent error modal
- **Operator Acknowledgment Required** - Error modal must persist until operator clicks 'X' to acknowledge
- **Clear Error Flags** - Provide operator button to reset error flags after issue resolution
- **Simplified Coordination** - No complex galvo or multi-signal coordination, focus on recoater coordination only

### Performance Considerations
- **Use async/await patterns** consistently throughout the backend
- **Implement connection pooling** for HTTP and OPC UA connections
- **Add appropriate timeouts** for all external communications
- **Use WebSocket subscriptions** for real-time status updates

## Common Pitfalls to Avoid

### 1. **Circular Dependencies**
- Import coordination engine in endpoints, not in global scope
- Use dependency injection for shared services
- Follow the established pattern in `backend/app/dependencies.py`

### 2. **State Management Issues**
- Use the global `multimaterial_job_state` instance consistently
- Don't create multiple job state instances
- Ensure thread-safe access to shared state

### 3. **Testing Shortcuts**
- Don't skip mock implementations for external services
- Always test error scenarios, not just happy paths
- Use the test data patterns established in existing tests

### 4. **Frontend Integration**
- Don't break existing single-layer functionality
- Maintain backward compatibility with current API endpoints
- Use existing component patterns and styling

## Development Environment Setup

### Required Tools
- Python 3.9+ with asyncio support
- Node.js 16+ for frontend development
- OPC UA test server for development (see `06-deployment-guide.md`)
- Mock Aerosint server (existing in `services/mock_recoater_client.py`)

### Environment Variables
```env
# OPC UA Server Configuration
OPCUA_SERVER_ENDPOINT=opc.tcp://0.0.0.0:4843
OPCUA_NAMESPACE_URI=http://recoater.backend.server
OPCUA_NAMESPACE_IDX=2

# Multi-Material Settings
MAX_CONCURRENT_JOBS=1
LAYER_UPLOAD_TIMEOUT=30.0
AEROSINT_POLLING_INTERVAL=5.0
DRUM_UPLOAD_DELAY=2.0
```

## Debugging and Troubleshooting

### Logging Strategy
- Use structured logging with correlation IDs for multi-component operations
- Log all state transitions in the coordination engine
- Include timing information for performance analysis
- Log all OPC UA variable changes for debugging

### Common Debug Scenarios
1. **OPC UA Connection Issues**: Check endpoint, namespace, and variable node IDs
2. **Coordination Timeouts**: Verify all components are responding within expected timeframes
3. **State Synchronization**: Ensure all drums reach ready state before proceeding
4. **CLI File Validation**: Verify identical layer counts across all 3 files

## Success Criteria

### Stage Completion Validation
- All tests pass for the current stage
- No regression in existing functionality
- Performance meets requirements (< 30 seconds per layer transition)
- Error handling works correctly for all failure scenarios
- Documentation is updated with any implementation-specific details

### Final System Validation
- Complete N-layer print job runs without manual intervention
- All 3 drums coordinate correctly throughout the process
- Error recovery works for hardware failures
- Operator interface provides clear status and control
