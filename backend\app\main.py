"""
Recoater HMI Backend - Main Application
=======================================

This is the main FastAPI application that serves as the backend for the
Recoater Custom HMI. It provides REST API endpoints and WebSocket connections
for real-time communication with the frontend.

The application acts as a proxy between the frontend and the recoater hardware,
providing a simplified and stable interface while handling all the complexity
of hardware communication.
"""

import os
import asyncio
import json
import logging
from typing import Dict, Any # Helps add "type hints" to variables
from contextlib import asynccontextmanager # Manages setup and teardown in asynchronous code

from fastapi import FastAPI, WebSocket, WebSocketDisconnect # Tools for handling HTTP requests and WebSocket connections
from fastapi.middleware.cors import CORSMiddleware # Tool to handle a web security feature called Cross-Origin Resource Sharing (CORS)
from dotenv import load_dotenv # Helper library to load configuration settings from a separate file (usually .env)

from services.recoater_client import RecoaterConnectionError, RecoaterAPIError
from app.dependencies import initialize_recoater_client, initialize_opcua_coordinator, get_recoater_client_instance
from app.utils.heartbeat import start_heartbeat_task, stop_heartbeat_task
# app.api.* are "routers" that group related API endpoints together
from app.api.status import router as status_router
from app.api.axis import router as axis_router
from app.api.recoater_controls import router as recoater_router
from app.api.print import router as print_router
from app.api.configuration import router as config_router

# Looks for .env in the project folder and loads any variables from it into the environment
load_dotenv()

# Configure logging
logging.basicConfig(
    level=getattr(logging, os.getenv("LOG_LEVEL", "INFO")),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# WebSocket connection manager
class ConnectionManager:
    """Manages WebSocket connections for real-time updates."""

    # Constructs the manager automatically everytime a new ConnectionManager is created and
    # initializes an empty active_connections list to keep track of every connected user
    def __init__(self):
        self.active_connections: list[WebSocket] = []
        # Track subscriptions for each connection
        self.connection_subscriptions: dict[WebSocket, set[str]] = {}
    
    # Handles a new user connecting.
    # Accepts the connection and add user's websocket object to the list
    async def connect(self, websocket: WebSocket):
        """Accept a new WebSocket connection."""
        await websocket.accept()
        self.active_connections.append(websocket)
        # Initialize with default subscription (status only)
        self.connection_subscriptions[websocket] = {'status'}
        logger.info(f"WebSocket connected. Total connections: {len(self.active_connections)}")

    # Handles a user disconnecting.
    # Removes the user's websocket object from the list
    def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection."""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        # Clean up subscription tracking
        if websocket in self.connection_subscriptions:
            del self.connection_subscriptions[websocket]
        logger.info(f"WebSocket disconnected. Total connections: {len(self.active_connections)}")

    def update_subscription(self, websocket: WebSocket, data_types: list[str]):
        """Update subscription preferences for a specific connection."""
        if websocket in self.connection_subscriptions:
            self.connection_subscriptions[websocket] = set(data_types)
            logger.info(f"Updated subscription for connection: {data_types}")
    
    # Loops through every active connection and sends the same message to all of them.
    # Includes error handling in case user disconnects unexpectedly
    async def broadcast(self, message: Dict[str, Any]):
        """Broadcast a message to all connected clients."""
        if not self.active_connections:
            return

        # Remove disconnected clients
        disconnected = []
        for connection in self.active_connections:
            try:
                # Filter message based on connection's subscriptions
                filtered_message = self._filter_message_for_connection(connection, message)
                if filtered_message:  # Only send if there's relevant data
                    await connection.send_json(filtered_message)
            except Exception as e:
                logger.warning(f"Failed to send message to WebSocket: {e}")
                disconnected.append(connection)

        # Clean up disconnected clients
        for connection in disconnected:
            self.disconnect(connection)

    def _filter_message_for_connection(self, websocket: WebSocket, message: Dict[str, Any]) -> Dict[str, Any]:
        """Filter message content based on connection's subscriptions."""
        if websocket not in self.connection_subscriptions:
            return message  # Send everything if no subscription info

        subscriptions = self.connection_subscriptions[websocket]
        filtered_message = {
            "type": message.get("type"),
            "timestamp": message.get("timestamp")
        }

        # Always include basic status data
        if "data" in message:
            filtered_message["data"] = message["data"]

        # Include optional data based on subscriptions
        if "axis" in subscriptions and "axis_data" in message:
            filtered_message["axis_data"] = message["axis_data"]
        if "drum" in subscriptions and "drum_data" in message:
            filtered_message["drum_data"] = message["drum_data"]
        if "leveler" in subscriptions and "leveler_data" in message:
            filtered_message["leveler_data"] = message["leveler_data"]
        if "print" in subscriptions and "print_data" in message:
            filtered_message["print_data"] = message["print_data"]

        return filtered_message

    def get_required_data_types(self) -> set[str]:
        """Get all data types that are currently subscribed to by any connection."""
        all_subscriptions = set()
        for subscriptions in self.connection_subscriptions.values():
            all_subscriptions.update(subscriptions)
        return all_subscriptions

# Creates a single, global instance of ConnectionManager that the whole application can use
manager = ConnectionManager()

async def _gather_single_drum_data(client: Any, drum_id: int) -> dict[str, Any]:
    """Gathers all status data for a single drum."""
    async def _safe_api_call(method, *args):
        try:
            return await asyncio.to_thread(method, *args)
        except (RecoaterConnectionError, RecoaterAPIError) as e:
            logger.debug(f"API call failed for drum {drum_id}: {e}")
            return None
        
    # Use asyncio.to_thread for synchronous client methods to avoid blocking
    info = await asyncio.to_thread(client.get_drum, drum_id)

    # If basic info fails, we can't proceed for this drum
    if info is None:
        logger.debug(f"Could not retrieve basic info for drum {drum_id}.")
        return {}
        
    return {
        "info": info,
        "motion": await _safe_api_call(client.get_drum_motion, drum_id),
        "ejection": await _safe_api_call(client.get_drum_ejection, drum_id),
        "suction": await _safe_api_call(client.get_drum_suction, drum_id),
        "blade_screws": await _safe_api_call(client.get_blade_screws_info, drum_id),
        "blade_motion": await _safe_api_call(client.get_blade_screws_motion, drum_id),
    }

async def _gather_all_drum_data(client: Any) -> Dict[str, Any]:
    """Gathers status data for all drums concurrently."""
    try:
        drums_info = await asyncio.to_thread(client.get_drums)
        if not isinstance(drums_info, list):
            logger.debug("Drums info is not a list. Cannot gather drum data.")
            return None
        
        all_drum_data = {}
        for drum_info in drums_info:
            drum_id = drum_info.get("id")
            if drum_id is not None:
                drum_data = await _gather_single_drum_data(client, drum_id)
                if drum_data:
                    all_drum_data[str(drum_id)] = drum_data
        return all_drum_data
    except (RecoaterConnectionError, RecoaterAPIError) as e:
        logger.debug(f"Drums data not available: {e}")
        return None
    
async def _gather_leveler_data(client: Any) -> Dict[str, Any]:
    """Gathers leveler status data."""
    try:
        return{
            "pressure": await asyncio.to_thread(client.get_leveler_pressure),
            "sensor": await asyncio.to_thread(client.get_leveler_sensor)
        }
    except (RecoaterConnectionError, RecoaterAPIError) as e:
        logger.debug(f"Leveler data not available: {e}")
        return None
    
async def _gather_print_data(client: Any) -> Dict[str, Any]:
    """Gathers print status data."""
    try:
        return{
            "layer_parameters": await asyncio.to_thread(client.get_layer_parameters),
            "job_status": await asyncio.to_thread(client.get_print_job_status)
        }
    except (RecoaterConnectionError, RecoaterAPIError) as e:
        logger.debug(f"Print data not available: {e}")
        return None

async def _gather_axis_data(client: Any) -> Dict[str, Any]:
    """Gathers axis status data."""
    try:
        # Note: Axis endpoints are not available in current hardware API
        # This is a placeholder for when axis control becomes available
        return {
            "x": {"position": 0.0, "running": False},
            "z": {"position": 0.0, "running": False},
            "gripper": {"enabled": False}
        }
    except (RecoaterConnectionError, RecoaterAPIError) as e:
        logger.debug(f"Axis data not available: {e}")
        return None
    
def _construct_status_message(status_data, axis_data, drum_data, leveler_data, print_data) -> Dict:
    """Constructs a status message to be sent over WebSocket."""
    return {
        "type": "status_update",
        "data": status_data,
        "axis_data": axis_data,
        "drum_data": drum_data,
        "leveler_data": leveler_data,
        "print_data": print_data,
        "timestamp": asyncio.get_event_loop().time()
    }

async def _broadcast_connection_error(error: Exception):
    """Broadcasts a connection error to all connected clients."""
    error_message = {
        "type": "connection_error",
        "error": str(error),
        "timestamp": asyncio.get_event_loop().time()
    }
    await manager.broadcast(error_message)


async def status_polling_task():
    """Background task that polls recoater status and broadcasts updates."""
    poll_interval = float(os.getenv("WEBSOCKET_POLL_INTERVAL", "1.0"))

    while True:
        try:
            recoater_client = get_recoater_client_instance()

            if not recoater_client or not manager.active_connections:
                await asyncio.sleep(poll_interval)
                continue

            # Get required data types from all active connections
            required_data_types = manager.get_required_data_types()
            logger.debug(f"Required data types: {list(required_data_types)}")

            # Always get basic status
            logger.debug("Calling: get_state()")
            main_status = await asyncio.to_thread(recoater_client.get_state)

            # Conditionally gather other data types based on subscriptions
            gather_tasks = []

            if "axis" in required_data_types:
                logger.debug("Gathering AXIS data: get_axis_*() calls")
                gather_tasks.append(_gather_axis_data(recoater_client))
            else:
                gather_tasks.append(asyncio.create_task(asyncio.sleep(0, result=None)))

            if "drum" in required_data_types:
                logger.debug("Gathering DRUM data: get_drums(), get_drum(), get_drum_motion(), get_drum_ejection(), get_drum_suction(), get_blade_screws_info(), get_blade_screws_motion() for each drum")
                gather_tasks.append(_gather_all_drum_data(recoater_client))
            else:
                gather_tasks.append(asyncio.create_task(asyncio.sleep(0, result=None)))

            if "leveler" in required_data_types:
                logger.debug("Gathering LEVELER data: get_leveler_pressure(), get_leveler_sensor()")
                gather_tasks.append(_gather_leveler_data(recoater_client))
            else:
                gather_tasks.append(asyncio.create_task(asyncio.sleep(0, result=None)))

            if "print" in required_data_types:
                logger.debug("Gathering PRINT data: get_layer_parameters(), get_print_job_status()")
                gather_tasks.append(_gather_print_data(recoater_client))
            else:
                gather_tasks.append(asyncio.create_task(asyncio.sleep(0, result=None)))

            # Execute only the required data gathering tasks
            axis_data, drum_data, leveler_data, print_data = await asyncio.gather(*gather_tasks)

            # Construct message
            message = _construct_status_message(
                main_status, axis_data, drum_data, leveler_data, print_data
            )

            # Broadcast message (filtering will happen per connection)
            await manager.broadcast(message)

        except (RecoaterConnectionError, RecoaterAPIError) as e:
            logger.error(f"Status polling connection error: {e}")
            await _broadcast_connection_error(e)

        except Exception as e:
            logger.error(f"Unexpected error in status polling: {e}", exc_info=True)

        await asyncio.sleep(poll_interval)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""

    # Startup
    logger.info("Starting Recoater HMI Backend...")

    # Initialize recoater client using the dependencies module
    initialize_recoater_client()

    # Initialize OPC UA coordinator for multi-material coordination
    await initialize_opcua_coordinator()

    # Start background status polling task
    polling_task = asyncio.create_task(status_polling_task())
    logger.info("Status polling task started")

    # Start heartbeat task to keep hardware connection alive
    heartbeat_task = start_heartbeat_task()
    logger.info("Heartbeat task started")

    logger.info("Backend startup complete")

    yield

    # Shutdown
    logger.info("Shutting down Recoater HMI Backend...")
    # Cancel status polling task
    polling_task.cancel()
    try:
        await polling_task
    except asyncio.CancelledError:
        pass
    logger.info("Status polling task stopped")

    # Stop heartbeat task
    try:
        await stop_heartbeat_task()
    except Exception as e:
        logger.error(f"Error stopping heartbeat task: {e}")
    else:
        logger.info("Heartbeat task stopped")

    logger.info("Backend shutdown complete")

# Create FastAPI application
app = FastAPI(
    title="Recoater HMI Backend",
    description="Backend API for the Aerosint SPD Recoater Custom HMI",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://127.0.0.1:5173"],  # Vite dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(status_router, prefix="/api/v1")
app.include_router(axis_router, prefix="/api/v1")
app.include_router(recoater_router, prefix="/api/v1")
app.include_router(print_router, prefix="/api/v1")
app.include_router(config_router, prefix="/api/v1")

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time status updates."""
    await manager.connect(websocket)
    try:
        while True:
            # Handle incoming messages for subscription updates
            message_text = await websocket.receive_text()
            try:
                message = json.loads(message_text)
                if message.get("type") == "subscribe" and "data_types" in message:
                    manager.update_subscription(websocket, message["data_types"])
                    logger.info(f"Updated subscription: {message['data_types']}")
            except json.JSONDecodeError:
                logger.warning(f"Invalid JSON received from WebSocket: {message_text}")
            except Exception as e:
                logger.error(f"Error processing WebSocket message: {e}")
    except WebSocketDisconnect:
        manager.disconnect(websocket)

@app.get("/")
async def root():
    """Root endpoint for basic health check."""
    return {
        "message": "Recoater HMI Backend",
        "version": "1.0.0",
        "status": "running"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )