"""
Multi-Material Job Manager Service
==================================

This service manages the lifecycle of multi-material print jobs with 3-drum coordination.
It handles job initialization, layer sequence management, progress tracking, error state
management, and coordinated layer upload management.

Based on Stage 2 requirements from the implementation documentation.
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any
from dataclasses import asdict

from app.models.multilayer_job import (
    MultiMaterialJobState, 
    JobStatus, 
    LayerData, 
    DrumState,
    multimaterial_job_state
)
from services.cli_parser import CliParserService, ParsedCliFile, CliParsingError
from services.recoater_client import RecoaterClient, RecoaterConnectionError, RecoaterAPIError

logger = logging.getLogger(__name__)


class MultiMaterialJobError(Exception):
    """Custom exception for multi-material job management errors."""
    pass


class MultiMaterialJobManager:
    """
    Service for managing multi-material print jobs with 3-drum coordination.
    
    This service provides high-level operations for:
    - Job initialization with 3 CLI files
    - Multi-drum layer sequence management
    - Progress tracking across all drums
    - Error state management per drum
    - Coordinated layer upload management
    """
    
    def __init__(self, recoater_client: RecoaterClient):
        """
        Initialize the Multi-Material Job Manager.
        
        Args:
            recoater_client: Client for communicating with recoater hardware
        """
        self.recoater_client = recoater_client
        self.cli_parser = CliParserService(logger=logger)
        self.job_state = multimaterial_job_state
        
        # Upload coordination settings
        self.drum_upload_delay = 2.0  # 2-second delay between drum uploads
        self.layer_upload_timeout = 30.0  # 30-second timeout for layer uploads
        
        logger.info("MultiMaterialJobManager initialized")
    
    async def initialize_job(self, file_ids: Dict[int, str], cli_file_cache: Dict[str, ParsedCliFile]) -> str:
        """
        Initialize a new multi-material job with 3 CLI files.
        
        Args:
            file_ids: Dictionary mapping drum_id (0,1,2) to file_id
            cli_file_cache: Cache of parsed CLI files
            
        Returns:
            str: The job ID of the initialized job
            
        Raises:
            MultiMaterialJobError: If initialization fails
        """
        try:
            logger.info(f"Initializing multi-material job with files: {file_ids}")
            
            # Validate input
            if len(file_ids) != 3:
                raise MultiMaterialJobError("Multi-material jobs require exactly 3 CLI files (one per drum)")
            
            if not all(drum_id in [0, 1, 2] for drum_id in file_ids.keys()):
                raise MultiMaterialJobError("Drum IDs must be 0, 1, and 2")
            
            # Reset job state
            self.job_state.reset_job()
            self.job_state.status = JobStatus.INITIALIZING
            self.job_state.file_ids = file_ids.copy()
            
            # Validate and process CLI files
            parsed_files = {}
            layer_counts = {}
            
            for drum_id, file_id in file_ids.items():
                if file_id not in cli_file_cache:
                    raise MultiMaterialJobError(f"CLI file {file_id} not found in cache for drum {drum_id}")
                
                parsed_data = cli_file_cache[file_id]
                parsed_files[drum_id] = parsed_data
                layer_counts[drum_id] = len(parsed_data.layers)
                
                logger.info(f"Drum {drum_id}: {layer_counts[drum_id]} layers from file {file_id}")
            
            # Calculate total layers as maximum across all drums
            self.job_state.total_layers = max(layer_counts.values())
            logger.info(f"Total layers calculated: {self.job_state.total_layers}")
            
            # Convert layers to LayerData objects for each drum
            for drum_id, parsed_data in parsed_files.items():
                # Store header lines for CLI generation
                self.job_state.header_lines[drum_id] = parsed_data.header_lines
                
                # Create LayerData objects
                layer_data_list = []
                for i, cli_layer in enumerate(parsed_data.layers):
                    layer_data = LayerData(
                        layer_number=i + 1,
                        z_height=cli_layer.z_height,
                        cli_layer=cli_layer,
                        drum_id=drum_id,
                        metadata={"original_file_id": file_id}
                    )
                    layer_data_list.append(layer_data)
                
                self.job_state.remaining_layers[drum_id] = layer_data_list
                
                # Update drum state
                drum_state = self.job_state.drums[drum_id]
                drum_state.file_id = file_id
                drum_state.total_layers = len(layer_data_list)
                drum_state.status = "initialized"
                
                logger.info(f"Drum {drum_id} initialized with {len(layer_data_list)} layers")
            
            # Set job as ready to start
            self.job_state.current_layer = 1  # Start with layer 1
            self.job_state.status = JobStatus.WAITING_FOR_PRINT_START
            
            logger.info(f"Multi-material job {self.job_state.job_id} initialized successfully")
            return self.job_state.job_id
            
        except Exception as e:
            error_msg = f"Failed to initialize multi-material job: {e}"
            logger.error(error_msg)
            self.job_state.set_error(error_msg)
            raise MultiMaterialJobError(error_msg) from e
    
    async def start_job(self) -> bool:
        """
        Start the multi-material job execution.
        
        Returns:
            bool: True if job started successfully
            
        Raises:
            MultiMaterialJobError: If job start fails
        """
        try:
            if self.job_state.status != JobStatus.WAITING_FOR_PRINT_START:
                raise MultiMaterialJobError(f"Cannot start job in status: {self.job_state.status}")
            
            logger.info(f"Starting multi-material job {self.job_state.job_id}")
            
            self.job_state.is_active = True
            self.job_state.start_time = time.time()
            self.job_state.status = JobStatus.RECOATER_ACTIVE
            
            # Reset all drum states for first layer
            for drum in self.job_state.drums.values():
                drum.is_ready = False
                drum.is_uploaded = False
                drum.status = "ready_for_upload"
            
            logger.info("Multi-material job started successfully")
            return True
            
        except Exception as e:
            error_msg = f"Failed to start multi-material job: {e}"
            logger.error(error_msg)
            self.job_state.set_error(error_msg)
            raise MultiMaterialJobError(error_msg) from e
    
    async def upload_current_layer_to_all_drums(self) -> bool:
        """
        Upload the current layer to all active drums with coordination delay.
        
        Returns:
            bool: True if all uploads successful
            
        Raises:
            MultiMaterialJobError: If upload fails
        """
        try:
            current_layer_data = self.job_state.get_current_layer_data()
            active_drums = self.job_state.get_active_drums()
            depleted_drums = self.job_state.get_depleted_drums()
            
            logger.info(f"Uploading layer {self.job_state.current_layer} to drums. Active: {active_drums}, Depleted: {depleted_drums}")
            
            upload_results = {}
            
            # Upload to active drums with delay between uploads
            for i, drum_id in enumerate([0, 1, 2]):
                try:
                    if drum_id in active_drums:
                        # Upload actual layer data
                        layer_data = current_layer_data[drum_id]
                        if layer_data:
                            cli_data = layer_data.generate_cli_data(self.job_state.header_lines[drum_id])
                            
                            logger.info(f"Uploading layer {self.job_state.current_layer} to drum {drum_id}")
                            result = self.recoater_client.upload_drum_geometry(
                                drum_id=drum_id,
                                file_data=cli_data,
                                content_type="application/octet-stream"
                            )
                            
                            upload_results[drum_id] = True
                            self.job_state.drums[drum_id].is_uploaded = True
                            self.job_state.drums[drum_id].last_layer_uploaded = self.job_state.current_layer
                            self.job_state.drums[drum_id].status = "uploaded"
                            
                            logger.info(f"Successfully uploaded layer to drum {drum_id}")
                        else:
                            logger.warning(f"No layer data for drum {drum_id} at layer {self.job_state.current_layer}")
                            upload_results[drum_id] = False
                    
                    elif drum_id in depleted_drums:
                        # Upload empty CLI for depleted drums to prevent memory retention
                        logger.info(f"Uploading empty CLI to depleted drum {drum_id}")
                        empty_cli_data = self._generate_empty_cli()
                        
                        result = self.recoater_client.upload_drum_geometry(
                            drum_id=drum_id,
                            file_data=empty_cli_data,
                            content_type="application/octet-stream"
                        )
                        
                        upload_results[drum_id] = True
                        self.job_state.drums[drum_id].is_uploaded = True
                        self.job_state.drums[drum_id].status = "depleted"
                        
                        logger.info(f"Successfully uploaded empty CLI to depleted drum {drum_id}")
                    
                    # Add delay between drum uploads (except for last drum)
                    if i < 2:  # Don't delay after the last drum
                        logger.debug(f"Waiting {self.drum_upload_delay}s before next drum upload")
                        await asyncio.sleep(self.drum_upload_delay)
                        
                except (RecoaterConnectionError, RecoaterAPIError) as e:
                    logger.error(f"Failed to upload to drum {drum_id}: {e}")
                    upload_results[drum_id] = False
                    self.job_state.drums[drum_id].status = "error"
                    self.job_state.drums[drum_id].error_message = str(e)
            
            # Check if all uploads were successful
            all_successful = all(upload_results.values())
            
            if all_successful:
                logger.info("All drum uploads completed successfully")
                # Mark all drums as ready for printing
                for drum in self.job_state.drums.values():
                    if drum.is_uploaded:
                        drum.is_ready = True
                        drum.status = "ready"
                
                return True
            else:
                failed_drums = [drum_id for drum_id, success in upload_results.items() if not success]
                error_msg = f"Upload failed for drums: {failed_drums}"
                logger.error(error_msg)
                self.job_state.set_error(error_msg)
                return False
                
        except Exception as e:
            error_msg = f"Failed to upload current layer to drums: {e}"
            logger.error(error_msg)
            self.job_state.set_error(error_msg)
            raise MultiMaterialJobError(error_msg) from e
    
    def _generate_empty_cli(self) -> bytes:
        """
        Generate an empty CLI file for depleted drums.
        
        Returns:
            bytes: Empty CLI file data
        """
        empty_cli_content = """$$HEADERSTART
$$BINARY 0
$$UNITS 1.0
$$VERSION 300
$$LABEL 1,Empty Layer
$$HEADEREND
$$GEOMETRYSTART
$$LAYER 0.0
$$GEOMETRYEND
"""
        return empty_cli_content.encode('ascii')
    
    async def cancel_job(self) -> bool:
        """
        Cancel the current multi-material job.
        
        Returns:
            bool: True if job cancelled successfully
        """
        try:
            logger.info(f"Cancelling multi-material job {self.job_state.job_id}")
            
            self.job_state.is_active = False
            self.job_state.status = JobStatus.CANCELLED
            
            # Reset all drum states
            for drum in self.job_state.drums.values():
                drum.is_ready = False
                drum.is_uploaded = False
                drum.status = "cancelled"
            
            logger.info("Multi-material job cancelled successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to cancel job: {e}")
            return False
    
    def get_job_status(self) -> Dict[str, Any]:
        """
        Get comprehensive job status information.
        
        Returns:
            Dict containing job status, progress, and drum states
        """
        return {
            "job_id": self.job_state.job_id,
            "is_active": self.job_state.is_active,
            "status": self.job_state.status.value,
            "current_layer": self.job_state.current_layer,
            "total_layers": self.job_state.total_layers,
            "progress_percentage": self.job_state.get_progress_percentage(),
            "estimated_time_remaining": self.job_state.get_estimated_time_remaining(),
            "start_time": self.job_state.start_time,
            "error_message": self.job_state.error_message,
            "drums": {
                drum_id: {
                    "file_id": drum.file_id,
                    "is_ready": drum.is_ready,
                    "is_uploaded": drum.is_uploaded,
                    "status": drum.status,
                    "error_message": drum.error_message,
                    "last_layer_uploaded": drum.last_layer_uploaded,
                    "total_layers": drum.total_layers
                }
                for drum_id, drum in self.job_state.drums.items()
            },
            "waiting_states": {
                "waiting_for_print_start": self.job_state.waiting_for_print_start,
                "waiting_for_layer_complete": self.job_state.waiting_for_layer_complete,
                "waiting_for_galvo": self.job_state.waiting_for_galvo
            }
        }
    
    def clear_error(self) -> bool:
        """
        Clear error state and return job to appropriate status.
        
        Returns:
            bool: True if error cleared successfully
        """
        try:
            logger.info("Clearing job error state")
            self.job_state.clear_error()
            
            # Clear drum error states
            for drum in self.job_state.drums.values():
                if drum.status == "error":
                    drum.status = "idle"
                    drum.error_message = ""
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to clear error state: {e}")
            return False


# Global instance for multi-material job management
multimaterial_job_manager: Optional[MultiMaterialJobManager] = None


def get_job_manager(recoater_client: RecoaterClient) -> MultiMaterialJobManager:
    """
    Get or create the global multi-material job manager instance.
    
    Args:
        recoater_client: Client for communicating with recoater hardware
        
    Returns:
        MultiMaterialJobManager: The global job manager instance
    """
    global multimaterial_job_manager
    
    if multimaterial_job_manager is None:
        multimaterial_job_manager = MultiMaterialJobManager(recoater_client)
    
    return multimaterial_job_manager
