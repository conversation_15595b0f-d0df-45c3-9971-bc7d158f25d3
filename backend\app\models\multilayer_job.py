"""
Multi-Material Job State Models
===============================

This module defines the data models for managing multi-material print jobs
with 3-drum coordination. It provides state management for job lifecycle,
drum coordination, and layer progression tracking.

Based on the Stage 2 requirements from the implementation documentation.
"""

import uuid
import time
from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, List, Optional, Any
from services.cli_parser import CliLayer


class JobStatus(Enum):
    """Multi-material job status enumeration"""
    IDLE = "idle"
    INITIALIZING = "initializing"
    WAITING_FOR_PRINT_START = "waiting_for_print_start"
    RECOATER_ACTIVE = "recoater_active"
    WAITING_FOR_DEPOSITION = "waiting_for_deposition"
    WAITING_FOR_GALVO = "waiting_for_galvo"
    LAYER_COMPLETE = "layer_complete"
    JOB_COMPLETE = "job_complete"
    ERROR = "error"
    CANCELLED = "cancelled"


@dataclass
class LayerData:
    """Individual layer information for multi-material printing"""
    layer_number: int
    z_height: float
    cli_layer: Cli<PERSON>ayer  # Reference to parsed CLI layer data
    drum_id: int  # 0, 1, or 2 for the three drums
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def generate_cli_data(self, header_lines: List[str]) -> bytes:
        """Generate ASCII CLI data for this layer on demand"""
        from services.cli_parser import CliParserService
        parser = CliParserService()
        return parser.generate_single_layer_ascii_cli(
            layer=self.cli_layer,
            header_lines=header_lines
        )


@dataclass
class DrumState:
    """State tracking for individual drums in multi-material jobs"""
    drum_id: int
    file_id: Optional[str] = None
    is_ready: bool = False
    is_uploaded: bool = False
    status: str = "idle"  # idle, uploading, ready, printing, error
    error_message: str = ""
    last_layer_uploaded: int = 0
    total_layers: int = 0
    
    def reset(self):
        """Reset drum state for new job"""
        self.file_id = None
        self.is_ready = False
        self.is_uploaded = False
        self.status = "idle"
        self.error_message = ""
        self.last_layer_uploaded = 0
        self.total_layers = 0


@dataclass
class MultiMaterialJobState:
    """Complete state management for multi-material print jobs with 3 drums"""
    
    # Job identification
    job_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    file_ids: Dict[int, str] = field(default_factory=dict)  # drum_id -> file_id mapping
    
    # Job scope and progress
    total_layers: int = 0
    current_layer: int = 0
    remaining_layers: Dict[int, List[LayerData]] = field(default_factory=dict)  # drum_id -> layers
    header_lines: Dict[int, List[str]] = field(default_factory=dict)  # drum_id -> CLI header lines
    
    # Job status
    is_active: bool = False
    status: JobStatus = JobStatus.IDLE
    
    # Timing information
    start_time: Optional[float] = None
    last_layer_time: Optional[float] = None
    estimated_completion: Optional[float] = None
    
    # Process state tracking
    waiting_for_print_start: bool = False
    waiting_for_layer_complete: bool = False
    waiting_for_galvo: bool = False
    
    # Error handling
    error_message: str = ""
    retry_count: int = 0
    
    # Drum coordination (3 drums: 0, 1, 2)
    drums: Dict[int, DrumState] = field(default_factory=lambda: {
        0: DrumState(drum_id=0),
        1: DrumState(drum_id=1),
        2: DrumState(drum_id=2)
    })
    
    def get_progress_percentage(self) -> float:
        """Calculate job progress as percentage"""
        if self.total_layers == 0:
            return 0.0
        # Progress is based on completed layers (current_layer - 1)
        completed_layers = max(0, self.current_layer - 1)
        return (completed_layers / self.total_layers) * 100.0
    
    def get_estimated_time_remaining(self) -> Optional[float]:
        """Estimate remaining time based on current progress"""
        if not self.start_time or self.current_layer <= 1:
            return None
        
        elapsed_time = time.time() - self.start_time
        completed_layers = max(1, self.current_layer - 1)
        time_per_layer = elapsed_time / completed_layers
        remaining_layers = self.total_layers - completed_layers
        
        return remaining_layers * time_per_layer
    
    def all_drums_ready(self) -> bool:
        """Check if all drums are ready for the current layer"""
        return all(drum.is_ready for drum in self.drums.values())
    
    def all_drums_uploaded(self) -> bool:
        """Check if all drums have uploaded the current layer"""
        return all(drum.is_uploaded for drum in self.drums.values())
    
    def get_active_drums(self) -> List[int]:
        """Get list of drum IDs that have layers remaining"""
        active_drums = []
        for drum_id, layers in self.remaining_layers.items():
            if layers:  # Has remaining layers
                active_drums.append(drum_id)
        return active_drums
    
    def get_current_layer_data(self) -> Dict[int, Optional[LayerData]]:
        """Get current layer data for all drums"""
        current_layers = {}
        for drum_id in [0, 1, 2]:
            layers = self.remaining_layers.get(drum_id, [])
            if layers and len(layers) >= self.current_layer:
                # Get layer by current_layer index (1-based to 0-based)
                layer_index = self.current_layer - 1
                if 0 <= layer_index < len(layers):
                    current_layers[drum_id] = layers[layer_index]
                else:
                    current_layers[drum_id] = None
            else:
                current_layers[drum_id] = None
        return current_layers
    
    def has_depleted_drums(self) -> bool:
        """Check if any drums have run out of layers"""
        for drum_id in [0, 1, 2]:
            layers = self.remaining_layers.get(drum_id, [])
            if not layers or len(layers) < self.current_layer:
                return True
        return False
    
    def get_depleted_drums(self) -> List[int]:
        """Get list of drum IDs that have no more layers"""
        depleted = []
        for drum_id in [0, 1, 2]:
            layers = self.remaining_layers.get(drum_id, [])
            if not layers or len(layers) < self.current_layer:
                depleted.append(drum_id)
        return depleted
    
    def advance_layer(self):
        """Advance to the next layer"""
        self.current_layer += 1
        self.last_layer_time = time.time()
        
        # Reset drum states for next layer
        for drum in self.drums.values():
            drum.is_ready = False
            drum.is_uploaded = False
            drum.status = "idle"
        
        # Check if job is complete
        if self.current_layer > self.total_layers:
            self.status = JobStatus.JOB_COMPLETE
            self.is_active = False
    
    def reset_job(self):
        """Reset job state for new execution"""
        self.job_id = str(uuid.uuid4())
        self.file_ids.clear()
        self.current_layer = 0
        self.total_layers = 0
        self.remaining_layers.clear()
        self.header_lines.clear()
        self.is_active = False
        self.status = JobStatus.IDLE
        self.start_time = None
        self.last_layer_time = None
        self.estimated_completion = None
        self.waiting_for_print_start = False
        self.waiting_for_layer_complete = False
        self.waiting_for_galvo = False
        self.error_message = ""
        self.retry_count = 0
        
        # Reset all drum states
        for drum in self.drums.values():
            drum.reset()
    
    def set_error(self, message: str):
        """Set job to error state with message"""
        self.status = JobStatus.ERROR
        self.error_message = message
        self.is_active = False
    
    def clear_error(self):
        """Clear error state and return to appropriate status"""
        self.error_message = ""
        if self.current_layer > self.total_layers:
            self.status = JobStatus.JOB_COMPLETE
        elif self.current_layer == 0:
            self.status = JobStatus.IDLE
        else:
            self.status = JobStatus.WAITING_FOR_PRINT_START


# Global instance for multi-material job state management
multimaterial_job_state = MultiMaterialJobState()
